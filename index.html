<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-T4N8L0SRWZ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-T4N8L0SRWZ');
  </script>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title id="page-title">SnowNavi Snow Club</title>
  <meta name="description" content="SnowNavi Snow Club offers all levels of snowboard and ski lessons and a interactive ski map designed for ski route planning. Join our courses and explore the slopes with confidence." />
  <meta name="keywords" content="ski lessons, snowboard lessons, interactive ski map, ski route planning, SnowNavi, ski club, BASI, instructor course, ski in Europe,滑雪俱乐部,滑雪课程,滑雪地图,滑雪自由式,中文滑雪课, Nederlands skiles" />
  <meta name="author" content="SnowNavi" />
  <meta property="og:title" content="SnowNavi Snow Club" />
  <meta property="og:description" content="All levels of ski and snowboard lessons and an interactive ski route planning map in English, 中文, and Nederlands. Since 2021." />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="assets/picture/snownavi_logo_banner.jpg" />
  <meta property="og:url" content="https://snownavi.ski" />
  <link rel="canonical" href="https://snownavi.ski" />

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="32x32" href="assets/picture/snownavi_logo.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/picture/snownavi_logo.png">
  <link rel="shortcut icon" href="assets/picture/snownavi_logo.png">

  <style>
    :root {
      --main-red: #E53512;
      --bg-light: #F9F4F3;
      --text-dark: #2F2F2F;
      --text-gray: #717171;
      --contrast-white: #FFFFFF;
      --accent-blue: #9ED4E7;
    }
    body {
      margin: 0;
      font-family: 'Noto Sans SC', sans-serif;
      background-color: var(--bg-light);
      color: var(--text-dark);
    }
    header {
      background: var(--contrast-white);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
      position: relative;
    }
    header img {
      height: 40px;
    }
    .menu-toggle {
      display: none;
      font-size: 1.5rem;
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-dark);
    }
    nav.nav-links {
      display: flex;
      align-items: center;
    }
    nav.nav-links a {
      margin-left: 1.5rem;
      text-decoration: none;
      color: var(--text-dark);
      font-weight: bold;
    }
    .language-selector {
      margin-left: 2rem;
      font-size: 1rem;
    }

    @media (max-width: 768px) {
      .menu-toggle {
        display: block;
      }
      nav.nav-links {
        display: none;
        flex-direction: column;
        background: var(--contrast-white);
        position: absolute;
        top: 100%;
        right: 0;
        width: 220px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
      }
      nav.nav-links.open {
        display: flex;
      }
      nav.nav-links a,
      .language-selector {
        margin: 1rem;
      }
    }

    nav {
      display: flex;
      align-items: center;
    }
    nav a {
      margin-left: 1.5rem;
      text-decoration: none;
      color: var(--text-dark);
      font-weight: bold;
    }
    .language-selector {
      margin-left: 2rem;
      font-size: 1rem;
    }
    .hero {
      background: url('assets/picture/hero.jpg') center/cover;
      color: white;
      padding: 6rem 2rem;
      text-align: center;
    }
    .hero h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    .hero .cta-btn {
      background: var(--main-red);
      color: white;
      padding: 0.8rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
    }
    .section {
      padding: 3rem 2rem;
      max-width: 1200px;
      margin: auto;
    }
    .courses {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }
    .course-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    }
    .course-card img {
      width: 100%;
      height: 180px;
      object-fit: cover;
    }
    .course-card h3 {
      margin: 1rem;
    }
    .course-card a {
      margin: 0 1rem 1rem;
      display: inline-block;
      background: var(--main-red);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      text-decoration: none;
    }
    .map-section, .story-section {
      text-align: center;
      margin: 4rem auto;
      padding: 0 2rem;
    }
    .map-section h2, .story-section h2 {
      margin-bottom: 1rem;
    }
    .map-section img {
      max-width: 100%;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      cursor: pointer;
    }
    .map-description {
      margin: 1.5rem 0;
      font-size: 1.1rem;
      line-height: 1.6;
    }
    .map-tagline {
      font-size: 1.3rem;
      font-weight: bold;
      color: var(--main-red);
      margin-bottom: 0.5rem;
    }
    .map-subtitle {
      font-size: 1.1rem;
      color: var(--text-gray);
      margin-bottom: 1.5rem;
    }
    .resort-list {
      margin: 1.5rem 0;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
    .resort-list h3 {
      color: var(--main-red);
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }
    .resort-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
    }
    .resort-item {
      background: var(--contrast-white);
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      text-align: center;
      transition: transform 0.2s ease;
    }
    .resort-item:hover {
      transform: translateY(-2px);
    }
    .resort-flag {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
    }
    .resort-name {
      font-weight: bold;
      color: var(--text-dark);
      margin-bottom: 0.3rem;
    }
    .resort-location {
      font-size: 0.9rem;
      color: var(--text-gray);
    }
    .timeline {
      max-width: 800px;
      margin: 2rem auto;
      text-align: left;
      display: none; /* Initially hidden, shown via JavaScript for Chinese */
    }
    .timeline-item {
      display: flex;
      margin-bottom: 2rem;
      align-items: flex-start;
    }
    .timeline-year {
      background: var(--main-red);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-weight: bold;
      min-width: 80px;
      text-align: center;
      margin-right: 1.5rem;
      flex-shrink: 0;
    }
    .timeline-content {
      flex: 1;
    }
    .timeline-content h3 {
      margin: 0 0 0.5rem 0;
      color: var(--text-dark);
      font-size: 1.2rem;
    }
    .timeline-location {
      color: var(--text-gray);
      font-size: 0.9rem;
      margin: 0 0 0.5rem 0;
      font-style: italic;
    }
    .timeline-content p {
      margin: 0 0 0.5rem 0;
      line-height: 1.6;
    }
    .timeline-future {
      font-style: italic;
      color: var(--main-red);
      font-weight: bold;
    }
    @media (max-width: 768px) {
      .timeline-item {
        flex-direction: column;
      }
      .timeline-year {
        margin-right: 0;
        margin-bottom: 0.5rem;
        align-self: flex-start;
      }
    }
    .contact-section {
      text-align: center;
      margin: 4rem auto;
      padding: 3rem 2rem;
      background: var(--contrast-white);
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      max-width: 800px;
    }
    .contact-section h2 {
      margin-bottom: 2rem;
      color: var(--text-dark);
    }
    .contact-intro {
      font-size: 1.1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
      color: var(--text-gray);
    }
    .contact-email {
      font-size: 1.2rem;
      font-weight: bold;
      color: var(--main-red);
      margin: 2rem 0;
    }
    .contact-email a {
      color: var(--main-red);
      text-decoration: none;
    }
    .contact-email a:hover {
      text-decoration: underline;
    }
    .social-media {
      margin-top: 2rem;
    }
    .social-media h3 {
      margin-bottom: 1.5rem;
      color: var(--text-dark);
    }
    .social-links {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
    }
    .social-link {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.8rem 1.5rem;
      background: var(--bg-light);
      border-radius: 8px;
      text-decoration: none;
      color: var(--text-dark);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      font-weight: 500;
      position: relative;
      cursor: pointer;
    }
    .social-link:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .social-icon {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }
    .qrcode-popup {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: var(--contrast-white);
      border-radius: 12px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
      padding: 1rem;
      margin-bottom: 0.5rem;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
      z-index: 1000;
      min-width: 200px;
      text-align: center;
    }
    .social-link:hover .qrcode-popup {
      opacity: 1;
      visibility: visible;
    }
    .qrcode-popup::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 8px solid transparent;
      border-top-color: var(--contrast-white);
    }
    .qrcode-image {
      width: 150px;
      height: 150px;
      object-fit: contain;
      border-radius: 8px;
      margin-bottom: 0.5rem;
    }
    .qrcode-text {
      font-size: 0.9rem;
      color: var(--text-gray);
      margin: 0;
    }
    @media (max-width: 768px) {
      .social-links {
        flex-direction: column;
        align-items: center;
      }
      .social-link {
        width: 200px;
        justify-content: center;
      }
    }
    footer {
      background: var(--text-dark);
      color: white;
      padding: 2rem;
      text-align: center;
    }
    #toTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      background: var(--main-red);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      display: none;
      z-index: 1000;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <header>
    <img src="assets/picture/snownavi_logo_banner.jpg" alt="SnowNavi logo">
    <button class="menu-toggle" id="menu-toggle">☰</button>
    <nav class="nav-links" id="nav-links">
      <!-- Navigation items will be dynamically inserted here -->
      <div class="language-selector">
        <select id="lang" class="language-selector">
          <option value="en">🇬🇧 EN</option>
          <option value="zh">🇨🇳 中文</option>
          <option value="nl">🇳🇱 NL</option>
        </select>
      </div>
    </nav>
  </header>

  <section class="hero" id="hero">
    <h1>Whether you're leveling up or starting fresh, we ride with you</h1>
    <a href="#courses" class="cta-btn">Join SnowNavi</a>
  </section>

  <section id="courses" class="section">
    <h2>Our Courses</h2>
    <div class="courses">
      <div class="course-card">
        <img src="assets/picture/basi.jpg" alt="BASI">
        <h3>BASI Instructor Course</h3>
        <a href="course.html?course=basi">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/park.jpg" alt="Park and Freestyle">
        <h3>Park Freestyle Lessons</h3>
        <a href="course.html?course=park">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/private.jpg" alt="Private">
        <h3>Private Lessons</h3>
        <a href="course.html?course=private">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/group.jpg" alt="Group">
        <h3>Group Lessons</h3>
        <a href="course.html?course=group">Learn More</a>
      </div>
      <div class="course-card">
        <img src="assets/picture/riding_week.jpg" alt="Riding Week">
        <h3>Riding Week</h3>
        <a href="course.html?course=riding_week">Learn More</a>
      </div>
    </div>
  </section>

  <section id="map" class="map-section">
    <h2>Interactive Ski Map</h2>

    <div class="map-description">
      <div class="map-tagline" id="map-tagline">Ultimate Ski Route Planner</div>
      <div class="map-subtitle" id="map-subtitle">Never get lost on snow again!</div>
    </div>

    <a href="https://snownavi.ski/preview" target="_blank">
      <img src="assets/picture/skimap.png" alt="Interactive Ski Map">
    </a>

    <div class="resort-list">
      <h3 id="resort-list-title">Currently Supported Resorts</h3>
      <div class="resort-grid">
        <div class="resort-item">
          <div class="resort-flag">🇫🇷</div>
          <div class="resort-name" id="resort-3valleys">Les 3 Vallées</div>
          <div class="resort-location" id="resort-3valleys-location">France</div>
        </div>
        <div class="resort-item">
          <div class="resort-flag">🇫🇷🇨🇭</div>
          <div class="resort-name" id="resort-portes-du-soleil">Portes du Soleil</div>
          <div class="resort-location" id="resort-portes-du-soleil-location">France & Switzerland</div>
        </div>
        <div class="resort-item">
          <div class="resort-flag">🇦🇩</div>
          <div class="resort-name" id="resort-pal-arinsal">Pal Arinsal</div>
          <div class="resort-location" id="resort-pal-arinsal-location">Andorra</div>
        </div>
      </div>
    </div>
  </section>

  <section id="story" class="story-section">
    <h2>🏂 SnowNavi的故事｜从雪友到雪匠</h2>
    <p>“Snow” stands for purity and passion, while “Navi” means guidance. We are committed to leading our partners through a fun ride on snow. — Since 2021 @ 🇳🇱</p>
    <div class="timeline timeline-zh">
      <div class="timeline-item">
        <div class="timeline-year">2017</div>
        <div class="timeline-content">
          <h3>起源：雪季的约定</h3>
          <p class="timeline-location">📍 荷兰·埃因霍温</p>
          <p>一群热爱冒险的朋友在圣诞假期自发前往阿尔卑斯山滑雪，从此开启了每年一次的集体雪上运动传统</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2019</div>
        <div class="timeline-content">
          <h3>成长：30人的雪山共鸣</h3>
          <p class="timeline-location">📍 法国·Val Thorens</p>
          <p>SnowNavi至今最大规模的雪上运动活动，30人大部队畅滑三峡谷"以雪会友"</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2021</div>
        <div class="timeline-content">
          <h3>转型：从热爱到专业</h3>
          <p class="timeline-location">📍 疫情后的新起点</p>
          <p>核心成员开始系统性学习滑雪教学，陆续考取CASI/BASI国际滑雪教练认证</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2023</div>
        <div class="timeline-content">
          <h3>新生：官方认证的里程碑</h3>
          <p class="timeline-location">📍 荷兰商会（KVK）</p>
          <p>随着荷兰雪上运动爱好者需求增长，SnowNavi正式注册为合法机构获得荷兰官方商业资质</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2024</div>
        <div class="timeline-content">
          <h3>桥梁：当欧洲遇见国货雪具</h3>
          <p class="timeline-location">📍 中欧滑雪生态圈</p>
          <p>携手Outdoor Master等国产雪具品牌尝试出海荷兰及欧洲市场，以装备赞助形式支持欧洲滑雪双板自由式俱乐部FreeGaze</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2025</div>
        <div class="timeline-content">
          <h3>革新：全欧滑雪的新篇章</h3>
          <p class="timeline-location">📍 荷兰SnowWorld</p>
          <p>与BASI官方合作伙伴SnowboardCoach深度合作，成为欧洲大陆唯一获得授权在室内雪场开展BASI滑雪指导员培训的组织</p>
          <p>独立开发并上线SnowNavi.ski在线雪场地图支持滑雪路线规划帮助小伙伴们探索全山不再迷路</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">未来</div>
        <div class="timeline-content">
          <h3>持续书写</h3>
          <p>从阿尔卑斯山的第一道雪痕，到与滑雪社群的携手合作，SnowNavi始终相信：滑雪的终极意义，在于打破边界，创造连接。</p>
          <p class="timeline-future">⛷️ To be continued...</p>
        </div>
      </div>
    </div>

    <!-- English Timeline -->
    <div class="timeline timeline-en">
      <div class="timeline-item">
        <div class="timeline-year">2017</div>
        <div class="timeline-content">
          <h3>Origin: The Snow Season Promise</h3>
          <p class="timeline-location">📍 Eindhoven, Netherlands</p>
          <p>A group of adventure-loving friends spontaneously headed to the Alps for Christmas skiing and snowboarding, starting an annual collective snow sports tradition</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2019</div>
        <div class="timeline-content">
          <h3>Growth: 30-Person Mountain Resonance</h3>
          <p class="timeline-location">📍 Val Thorens, France</p>
          <p>SnowNavi's largest snow sports event to date, with 30 people conquering the Three Valleys under the motto "Making friends through snow"</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2021</div>
        <div class="timeline-content">
          <h3>Transformation: From Passion to Professional</h3>
          <p class="timeline-location">📍 Post-pandemic New Beginning</p>
          <p>Core members began systematic ski and snowboard instruction training, progressively obtaining CASI/BASI international ski and snowboard instructor certifications</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2023</div>
        <div class="timeline-content">
          <h3>Rebirth: Official Certification Milestone</h3>
          <p class="timeline-location">📍 Dutch Chamber of Commerce (KVK)</p>
          <p>With growing demand from Dutch snow sports enthusiasts, SnowNavi officially registered as a legal entity, obtaining Dutch official business qualifications</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2024</div>
        <div class="timeline-content">
          <h3>Bridge: When Europe Meets Chinese Snow Gear</h3>
          <p class="timeline-location">📍 China-Europe Skiing Ecosystem</p>
          <p>Partnered with Chinese snow gear brands like Outdoor Master to explore the Dutch and European markets, supporting European freestyle ski club FreeGaze through equipment sponsorship</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2025</div>
        <div class="timeline-content">
          <h3>Innovation: New Chapter for European Snow Sports</h3>
          <p class="timeline-location">📍 SnowWorld, Netherlands</p>
          <p>Deep collaboration with BASI official partner SnowboardCoach, becoming the only organization authorized to conduct BASI ski and snowboard instructor training at indoor snow facilities in the European continent</p>
          <p>Independently developed and launched SnowNavi.ski online ski map supporting ski and snowboard route planning, helping friends explore the entire mountain without getting lost</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">Future</div>
        <div class="timeline-content">
          <h3>Continuing to Write</h3>
          <p>From the first snow tracks in the Alps to collaborating with snow sports communities, SnowNavi always believes: the ultimate meaning of snow sports lies in breaking boundaries and creating connections.</p>
          <p class="timeline-future">⛷️ To be continued...</p>
        </div>
      </div>
    </div>

    <!-- Dutch Timeline -->
    <div class="timeline timeline-nl">
      <div class="timeline-item">
        <div class="timeline-year">2017</div>
        <div class="timeline-content">
          <h3>Oorsprong: De Sneeuwseizoen Belofte</h3>
          <p class="timeline-location">📍 Eindhoven, Nederland</p>
          <p>Een groep avontuurlijke vrienden ging spontaan naar de Alpen voor kerst skiën en snowboarden, wat het begin was van een jaarlijkse collectieve wintersport traditie</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2019</div>
        <div class="timeline-content">
          <h3>Groei: 30-Persoons Berg Resonantie</h3>
          <p class="timeline-location">📍 Val Thorens, Frankrijk</p>
          <p>SnowNavi's grootste wintersport evenement tot nu toe, met 30 mensen die de Drie Valleien veroverden onder het motto "Vrienden maken door sneeuw"</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2021</div>
        <div class="timeline-content">
          <h3>Transformatie: Van Passie naar Professioneel</h3>
          <p class="timeline-location">📍 Post-pandemie Nieuw Begin</p>
          <p>Kernleden begonnen systematische ski- en snowboard-instructie training, geleidelijk CASI/BASI internationale ski- en snowboard-instructeur certificeringen behaald</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2023</div>
        <div class="timeline-content">
          <h3>Wedergeboorte: Officiële Certificering Mijlpaal</h3>
          <p class="timeline-location">📍 Nederlandse Kamer van Koophandel (KVK)</p>
          <p>Met groeiende vraag van Nederlandse wintersport liefhebbers, registreerde SnowNavi zich officieel als juridische entiteit, Nederlandse officiële bedrijfskwalificaties verkregen</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2024</div>
        <div class="timeline-content">
          <h3>Brug: Wanneer Europa Chinese Sneeuw Uitrusting Ontmoet</h3>
          <p class="timeline-location">📍 China-Europa Ski Ecosysteem</p>
          <p>Samenwerking met Chinese sneeuw uitrusting merken zoals Outdoor Master om de Nederlandse en Europese markten te verkennen, ondersteuning van Europese freestyle ski club FreeGaze door uitrusting sponsoring</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">2025</div>
        <div class="timeline-content">
          <h3>Innovatie: Nieuw Hoofdstuk voor Europese Wintersport</h3>
          <p class="timeline-location">📍 SnowWorld, Nederland</p>
          <p>Diepe samenwerking met BASI officiële partner SnowboardCoach, de enige organisatie geautoriseerd om BASI ski- en snowboard-instructeur training uit te voeren in indoor sneeuw faciliteiten op het Europese continent</p>
          <p>Onafhankelijk ontwikkeld en gelanceerd SnowNavi.ski online ski kaart die ski en snowboard route planning ondersteunt, vrienden helpt de hele berg te verkennen zonder te verdwalen</p>
        </div>
      </div>

      <div class="timeline-item">
        <div class="timeline-year">Toekomst</div>
        <div class="timeline-content">
          <h3>Blijven Schrijven</h3>
          <p>Van de eerste sneeuwsporen in de Alpen tot samenwerking met wintersport gemeenschappen, SnowNavi gelooft altijd: de ultieme betekenis van wintersport ligt in het doorbreken van grenzen en het creëren van verbindingen.</p>
          <p class="timeline-future">⛷️ Wordt vervolgd...</p>
        </div>
      </div>
    </div>
  </section>

  <section id="contact" class="contact-section">
    <h2 id="contact-title">联系我们</h2>
    <div class="contact-intro" id="contact-intro">
      <p>想一起刷雪道？想学点新技巧？还是有什么酷炫的合作想法？</p>
      <p>随时来撩我们！我们这群雪痴随时准备和你聊聊雪山上的那些事儿 🏂</p>
    </div>

    <div class="contact-email">
      <span id="contact-email-label">邮箱联系：</span>
      <a href="mailto:<EMAIL>"><EMAIL></a>
    </div>

    <div class="social-media">
      <h3 id="social-media-title">关注我们</h3>
      <div class="social-links">
        <a href="#" class="social-link" id="wechat-link">
          <img src="assets/picture/wechat_logo.png" alt="WeChat" class="social-icon">
          <span id="wechat-text">SnowNavi指雪针</span>
          <div class="qrcode-popup">
            <img src="assets/picture/wechat_qrcode.jpg" alt="WeChat QR Code" class="qrcode-image">
            <p class="qrcode-text" id="wechat-qr-text">扫码关注微信公众号</p>
          </div>
        </a>
        <a href="#" class="social-link" id="xiaohongshu-link">
          <img src="assets/picture/xiaohongshu_logo.png" alt="Xiaohongshu" class="social-icon">
          <span id="xiaohongshu-text">SnowNavi指雪针</span>
          <div class="qrcode-popup">
            <img src="assets/picture/xiaohongshu_qrcode.jpg" alt="Xiaohongshu QR Code" class="qrcode-image">
            <p class="qrcode-text" id="xiaohongshu-qr-text">扫码关注小红书</p>
          </div>
        </a>
      </div>
    </div>
  </section>

  <footer>
    <p>Contact: <EMAIL> | Follow our 微信公共号 SnowNavi指雪针 for updates</p>
    <p>&copy; 2025 SnowNavi Sports. All rights reserved.</p>
  </footer>

  <button id="toTop" onclick="window.scrollTo({ top: 0, behavior: 'smooth' })">↑ Top</button>

  <!-- Include the navigation module -->
  <script src="assets/js/navigation.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const zhTexts = {
        title: 'SnowNavi 滑雪俱乐部',
        navCourses: '课程',
        navMap: '在线滑雪地图',
        navContact: '联系我们',
        navStory: '我们的故事',
        navMember: '会员',
        heroText: '无论是进阶挑战，还是初学启蒙，我们陪你畅滑到底',
        ctaBtn: '加入 SnowNavi ',
        sectionTitle: '我们的课程',
        courses: ['BASI 指导员课程', '公园自由式课程', '私教课程', '团体课程', '畅滑周'],
        learnMore: '了解更多',
        mapTitle: '在线滑雪地图',
        mapTagline: '终极滑雪路线规划器',
        mapSubtitle: '再也不会在雪山上迷路！',
        resortListTitle: '当前支持的雪场',
        resort3valleys: '法国三峡谷',
        resort3valleysLocation: '法国',
        resortPortesDuSoleil: '太阳门',
        resortPortesDuSoleilLocation: '法国 & 瑞士',
        resortPalArinsal: 'Pal Arinsal',
        resortPalArinsalLocation: '安道尔',
        storyTitle: '🏂 SnowNavi的故事｜从雪友到雪匠',
        storyText: '“Snow”即雪，象征着纯洁与激情；“Navi”意为指引。我们致力于以雪为引，引领伙伴们畅享自由的滑雪之旅。- Since 2021 @ 🇳🇱',
        contactTitle: '联系我们',
        contactIntro: ['想一起刷雪道？想学点新技巧？还是有什么酷炫的合作想法？', '随时来撩我们！我们这群雪痴随时准备和你聊聊雪山上的那些事儿 🏂'],
        contactEmailLabel: '邮箱联系：',
        socialMediaTitle: '关注我们',
        wechatText: 'SnowNavi指雪针',
        xiaohongshuText: 'SnowNavi指雪针',
        wechatQrText: '扫码关注微信公众号',
        xiaohongshuQrText: '扫码关注小红书',
        contactText: '联系方式：<EMAIL> | 请关注微信公共号： SnowNavi指雪针',
        copyright: '保留所有权利.'
      };

      const enTexts = {
        title: 'SnowNavi Snow Club',
        navCourses: 'Courses',
        navMap: 'Interactive Ski Map',
        navContact: 'Contact',
        navStory: 'Our Story',
        navMember: 'Member',
        heroText: "Whether you're leveling up or starting fresh, we ride with you",
        ctaBtn: 'Join SnowNavi',
        sectionTitle: 'Our Courses',
        courses: ['BASI Instructor Course', 'Park Freestyle Lessons', 'Private Lessons', 'Group Lessons', 'Riding Week'],
        learnMore: 'Learn More',
        mapTitle: 'Interactive Ski Map',
        mapTagline: 'Ultimate Ski Route Planner',
        mapSubtitle: 'Never get lost on snow again!',
        resortListTitle: 'Currently Supported Resorts',
        resort3valleys: 'Les 3 Vallées',
        resort3valleysLocation: 'France',
        resortPortesDuSoleil: 'Portes du Soleil',
        resortPortesDuSoleilLocation: 'France & Switzerland',
        resortPalArinsal: 'Pal Arinsal',
        resortPalArinsalLocation: 'Andorra',
        storyTitle: '🏂 SnowNavi\'s Story | From Snow Friends to Snow Masters',
        storyText: '“Snow” stands for purity and passion, while “Navi” means guidance. We are committed to leading our partners through a fun ride on snow. — Since 2021 @ 🇳🇱',
        contactTitle: 'Contact Us',
        contactIntro: ['Wanna shred some slopes together? Looking to level up your skills? Got some rad collaboration ideas?', 'Hit us up! We\'re a bunch of snow addicts always down to chat about mountain adventures 🏂'],
        contactEmailLabel: 'Email Contact:',
        socialMediaTitle: 'Follow Us',
        wechatText: 'SnowNavi指雪针',
        xiaohongshuText: 'SnowNavi指雪针',
        wechatQrText: 'Scan to follow WeChat',
        xiaohongshuQrText: 'Scan to follow Xiaohongshu',
        contactText: 'Contact: <EMAIL> | Follow us on 微信公共号： SnowNavi指雪针',
        copyright: 'All rights reserved.'
      };

      const nlTexts = {
        title: 'SnowNavi Snow Club',
        navCourses: 'Cursussen',
        navMap: 'Interactieve Skikaart',
        navContact: 'Contact',
        navStory: 'Ons Verhaal',
        navMember: 'Lid',
        heroText: 'Of je nu verder wilt groeien of net begint, wij gaan samen de piste af',
        ctaBtn: 'Doe mee met SnowNavi',
        sectionTitle: 'Onze Cursussen',
        courses: ['BASI Instructeurs cursus', 'Freestyle Park lessen', 'Privé lessen', 'Groeps lessen', 'Riding Week'],
        learnMore: 'Meer info',
        mapTitle: 'Interactieve Skikaart',
        mapTagline: 'Ultieme Ski Route Planner',
        mapSubtitle: 'Raak nooit meer verdwaald in de sneeuw!',
        resortListTitle: 'Momenteel Ondersteunde Skigebieden',
        resort3valleys: 'Les 3 Vallées',
        resort3valleysLocation: 'Frankrijk',
        resortPortesDuSoleil: 'Portes du Soleil',
        resortPortesDuSoleilLocation: 'Frankrijk & Zwitserland',
        resortPalArinsal: 'Pal Arinsal',
        resortPalArinsalLocation: 'Andorra',
        storyTitle: '🏂 SnowNavi\'s Verhaal | Van Sneeuwvrienden tot Sneeuwmeesters',
        storyText: '“Snow” staat voor puurheid en passie, terwijl “Navi” richting betekent. Wij zetten ons in om onze partners mee te nemen op een plezierige rit door de sneeuw. — Sinds 2021 @ 🇳🇱',
        contactTitle: 'Contact',
        contactIntro: ['Zin om samen de pistes af te knallen? Wil je nieuwe tricks leren? Of heb je coole samenwerkingsideeën?', 'Stuur ons een berichtje! Wij zijn een stel sneeuwfanaten die altijd klaar staan voor een chat over bergavonturen 🏂'],
        contactEmailLabel: 'Email Contact:',
        socialMediaTitle: 'Volg Ons',
        wechatText: 'SnowNavi指雪针',
        xiaohongshuText: 'SnowNavi指雪针',
        wechatQrText: 'Scan om WeChat te volgen',
        xiaohongshuQrText: 'Scan om Xiaohongshu te volgen',
        contactText: 'Contact: <EMAIL> | Volg ons op 微信公共号: SnowNavi指雪针',
        copyright: 'Alle rechten voorbehouden.'
      };

      const langSelect = document.getElementById('lang');
      if (!langSelect) return;

      function setLang(lang) {
        const texts = lang === 'zh' ? zhTexts : lang === 'nl' ? nlTexts : enTexts;
        document.getElementById('page-title').textContent = texts.title;
        document.querySelector('.hero h1').textContent = texts.heroText;
        document.querySelector('.hero .cta-btn').textContent = texts.ctaBtn;
        document.querySelector('#courses h2').textContent = texts.sectionTitle;
        document.querySelectorAll('.course-card h3').forEach((el, i) => {
          if (texts.courses[i]) el.textContent = texts.courses[i];
        });
        document.querySelectorAll('.course-card a').forEach(el => {
          el.textContent = texts.learnMore;
        });
        document.querySelector('#map h2').textContent = texts.mapTitle;

        // Update map description elements
        const mapTagline = document.getElementById('map-tagline');
        const mapSubtitle = document.getElementById('map-subtitle');
        const resortListTitle = document.getElementById('resort-list-title');
        const resort3valleys = document.getElementById('resort-3valleys');
        const resort3valleysLocation = document.getElementById('resort-3valleys-location');
        const resortPortesDuSoleil = document.getElementById('resort-portes-du-soleil');
        const resortPortesDuSoleilLocation = document.getElementById('resort-portes-du-soleil-location');
        const resortPalArinsal = document.getElementById('resort-pal-arinsal');
        const resortPalArinsalLocation = document.getElementById('resort-pal-arinsal-location');

        if (mapTagline) mapTagline.textContent = texts.mapTagline;
        if (mapSubtitle) mapSubtitle.textContent = texts.mapSubtitle;
        if (resortListTitle) resortListTitle.textContent = texts.resortListTitle;
        if (resort3valleys) resort3valleys.textContent = texts.resort3valleys;
        if (resort3valleysLocation) resort3valleysLocation.textContent = texts.resort3valleysLocation;
        if (resortPortesDuSoleil) resortPortesDuSoleil.textContent = texts.resortPortesDuSoleil;
        if (resortPortesDuSoleilLocation) resortPortesDuSoleilLocation.textContent = texts.resortPortesDuSoleilLocation;
        if (resortPalArinsal) resortPalArinsal.textContent = texts.resortPalArinsal;
        if (resortPalArinsalLocation) resortPalArinsalLocation.textContent = texts.resortPalArinsalLocation;

        document.querySelector('#story h2').textContent = texts.storyTitle;
        // Hide the old paragraph and show appropriate timeline
        const oldParagraph = document.querySelector('#story p');
        const timelineZh = document.querySelector('#story .timeline-zh');
        const timelineEn = document.querySelector('#story .timeline-en');
        const timelineNl = document.querySelector('#story .timeline-nl');

        // Hide old paragraph and all timelines first
        if (oldParagraph) oldParagraph.style.display = 'none';
        if (timelineZh) timelineZh.style.display = 'none';
        if (timelineEn) timelineEn.style.display = 'none';
        if (timelineNl) timelineNl.style.display = 'none';

        // Show appropriate timeline based on language
        if (lang === 'zh' && timelineZh) {
          timelineZh.style.display = 'block';
        } else if (lang === 'en' && timelineEn) {
          timelineEn.style.display = 'block';
        } else if (lang === 'nl' && timelineNl) {
          timelineNl.style.display = 'block';
        }

        // Update contact section
        const contactTitle = document.getElementById('contact-title');
        const contactIntro = document.getElementById('contact-intro');
        const contactEmailLabel = document.getElementById('contact-email-label');
        const socialMediaTitle = document.getElementById('social-media-title');
        const wechatText = document.getElementById('wechat-text');
        const xiaohongshuText = document.getElementById('xiaohongshu-text');
        const wechatQrText = document.getElementById('wechat-qr-text');
        const xiaohongshuQrText = document.getElementById('xiaohongshu-qr-text');

        if (contactTitle) contactTitle.textContent = texts.contactTitle;
        if (contactIntro) {
          contactIntro.innerHTML = texts.contactIntro.map(text => `<p>${text}</p>`).join('');
        }
        if (contactEmailLabel) contactEmailLabel.textContent = texts.contactEmailLabel;
        if (socialMediaTitle) socialMediaTitle.textContent = texts.socialMediaTitle;
        if (wechatText) wechatText.textContent = texts.wechatText;
        if (xiaohongshuText) xiaohongshuText.textContent = texts.xiaohongshuText;
        if (wechatQrText) wechatQrText.textContent = texts.wechatQrText;
        if (xiaohongshuQrText) xiaohongshuQrText.textContent = texts.xiaohongshuQrText;

        // Update footer (simplified)
        document.querySelector('footer p:first-child').textContent = texts.contactText;
        document.querySelector('footer p:last-child').innerHTML = `&copy; 2025 ${lang === 'zh' ? 'SnowNavi 滑雪俱乐部' : 'SnowNavi Sports'}. ${texts.copyright}`;

        // Trigger navigation update with the new language
        if (window.navigationManager) {
          window.navigationManager.currentLang = lang;
          window.navigationManager.renderNavigation();
        }
      }

      const savedLang = localStorage.getItem('preferredLang') || (navigator.language.startsWith('zh') ? 'zh' : navigator.language.startsWith('nl') ? 'nl' : 'en');
      langSelect.value = savedLang;
      setLang(savedLang);

      // Ensure navigation bar language is synchronized
      if (window.navigationManager) {
        window.navigationManager.currentLang = savedLang;
        window.navigationManager.renderNavigation();
      }

      langSelect.addEventListener('change', e => {
        const newLang = e.target.value;
        localStorage.setItem('preferredLang', newLang);
        setLang(newLang);
      });

      // Return to Top logic
      const toTop = document.getElementById('toTop');
      const hero = document.getElementById('hero');

      window.addEventListener('scroll', function () {
        const heroBottom = hero.getBoundingClientRect().bottom;
        toTop.style.display = heroBottom < 0 ? 'block' : 'none';
      });
    });

    // URL转换逻辑：若包含 ?loc= 则重定向到新格式
    (function () {
      const currentUrl = new URL(window.location.href);
      const locParams = currentUrl.searchParams.getAll('loc');

      if (locParams.length > 0) {
        // 将纬度,经度 => 经度,纬度
        const reversedCoords = locParams.map(coord => {
          const [lat, lng] = coord.split(',');
          return `${lng},${lat}`;
        });

        const joinedCoords = reversedCoords.join(';');
        const newUrl = `https://snownavi.ski/preview/?coords=${joinedCoords}&resortKey=3valley`;

        // 重定向到新URL
        window.location.replace(newUrl);
      }
    })();

    document.getElementById('menu-toggle').addEventListener('click', function () {
      document.getElementById('nav-links').classList.toggle('open');
    });
  </script>
</body>
</html>
