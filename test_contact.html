<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Section Test</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
        }
        body {
            margin: 0;
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
            padding: 2rem;
        }
        .contact-section {
            text-align: center;
            margin: 4rem auto;
            padding: 3rem 2rem;
            background: var(--contrast-white);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 800px;
        }
        .contact-section h2 {
            margin-bottom: 2rem;
            color: var(--text-dark);
        }
        .contact-intro {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: var(--text-gray);
        }
        .contact-email {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--main-red);
            margin: 2rem 0;
        }
        .contact-email a {
            color: var(--main-red);
            text-decoration: none;
        }
        .contact-email a:hover {
            text-decoration: underline;
        }
        .social-media {
            margin-top: 2rem;
        }
        .social-media h3 {
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }
        .social-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        .social-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: var(--bg-light);
            border-radius: 8px;
            text-decoration: none;
            color: var(--text-dark);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            font-weight: 500;
        }
        .social-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .social-icon {
            width: 24px;
            height: 24px;
            object-fit: contain;
        }
        .language-selector {
            margin: 2rem 0;
        }
        select {
            padding: 0.5rem;
            font-size: 1rem;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="language-selector">
        <label for="lang">Language: </label>
        <select id="lang">
            <option value="en">🇬🇧 EN</option>
            <option value="zh">🇨🇳 中文</option>
            <option value="nl">🇳🇱 NL</option>
        </select>
    </div>

    <section id="contact" class="contact-section">
        <h2 id="contact-title">联系我们</h2>
        <div class="contact-intro" id="contact-intro">
            <p>无论您是想了解更多课程信息，还是寻求合作机会，我们都欢迎您的联系。</p>
            <p>我们的专业团队将竭诚为您提供最优质的滑雪体验和服务。</p>
        </div>

        <div class="contact-email">
            <span id="contact-email-label">邮箱联系：</span>
            <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>

        <div class="social-media">
            <h3 id="social-media-title">关注我们</h3>
            <div class="social-links">
                <a href="#" class="social-link" id="wechat-link">
                    <img src="assets/picture/wechat_logo.png" alt="WeChat" class="social-icon">
                    <span id="wechat-text">SnowNavi指雪针</span>
                </a>
                <a href="#" class="social-link" id="xiaohongshu-link">
                    <img src="assets/picture/xiaohongshu_logo.png" alt="Xiaohongshu" class="social-icon">
                    <span id="xiaohongshu-text">SnowNavi指雪针</span>
                </a>
            </div>
        </div>
    </section>

    <script>
        const zhTexts = {
            contactTitle: '联系我们',
            contactIntro: ['无论您是想了解更多课程信息，还是寻求合作机会，我们都欢迎您的联系。', '我们的专业团队将竭诚为您提供最优质的滑雪体验和服务。'],
            contactEmailLabel: '邮箱联系：',
            socialMediaTitle: '关注我们',
            wechatText: 'SnowNavi指雪针',
            xiaohongshuText: 'SnowNavi指雪针'
        };

        const enTexts = {
            contactTitle: 'Contact Us',
            contactIntro: ['Whether you want to learn more about our courses or explore collaboration opportunities, we welcome your contact.', 'Our professional team is dedicated to providing you with the highest quality skiing experience and service.'],
            contactEmailLabel: 'Email Contact:',
            socialMediaTitle: 'Follow Us',
            wechatText: 'SnowNavi指雪针',
            xiaohongshuText: 'SnowNavi指雪针'
        };

        const nlTexts = {
            contactTitle: 'Contact',
            contactIntro: ['Of je nu meer wilt weten over onze cursussen of samenwerkingsmogelijkheden wilt verkennen, we verwelkomen je contact.', 'Ons professionele team zet zich in om je de hoogste kwaliteit ski-ervaring en service te bieden.'],
            contactEmailLabel: 'Email Contact:',
            socialMediaTitle: 'Volg Ons',
            wechatText: 'SnowNavi指雪针',
            xiaohongshuText: 'SnowNavi指雪针'
        };

        function setLang(lang) {
            const texts = lang === 'zh' ? zhTexts : lang === 'nl' ? nlTexts : enTexts;

            document.getElementById('contact-title').textContent = texts.contactTitle;
            document.getElementById('contact-intro').innerHTML = texts.contactIntro.map(text => `<p>${text}</p>`).join('');
            document.getElementById('contact-email-label').textContent = texts.contactEmailLabel;
            document.getElementById('social-media-title').textContent = texts.socialMediaTitle;
            document.getElementById('wechat-text').textContent = texts.wechatText;
            document.getElementById('xiaohongshu-text').textContent = texts.xiaohongshuText;
        }

        document.getElementById('lang').addEventListener('change', function(e) {
            setLang(e.target.value);
        });

        // Initialize with Chinese
        setLang('zh');
    </script>
</body>
</html>
