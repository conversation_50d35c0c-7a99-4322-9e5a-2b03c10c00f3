<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partners Section Test</title>
    <style>
        :root {
            --main-red: #E53512;
            --bg-light: #F9F4F3;
            --text-dark: #2F2F2F;
            --text-gray: #717171;
            --contrast-white: #FFFFFF;
        }
        body {
            margin: 0;
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
            padding: 2rem;
        }
        .partners-section {
            text-align: center;
            margin: 4rem auto;
            padding: 3rem 2rem;
            background: var(--contrast-white);
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 800px;
        }
        .partners-section h2 {
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        .partners-intro {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 3rem;
            color: var(--text-gray);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        .partner-card {
            background: var(--contrast-white);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }
        .partner-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .partner-logo {
            width: 120px;
            height: 80px;
            object-fit: contain;
            margin-bottom: 1.5rem;
            border-radius: 8px;
        }
        .partner-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 1rem;
        }
        .partner-description {
            font-size: 1rem;
            color: var(--text-gray);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        .partner-link {
            display: inline-block;
            background: var(--main-red);
            color: white;
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }
        .partner-link:hover {
            background: #c42e0f;
        }
        .language-selector {
            margin: 2rem 0;
        }
        select {
            padding: 0.5rem;
            font-size: 1rem;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .demo-note {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="language-selector">
        <label for="lang">Language: </label>
        <select id="lang">
            <option value="en">🇬🇧 EN</option>
            <option value="zh">🇨🇳 中文</option>
            <option value="nl">🇳🇱 NL</option>
        </select>
    </div>

    <div class="demo-note">
        <strong>演示说明：</strong>这是Partner板块的独立测试页面。在实际网站中，这个板块位于Our Story和Contact Us之间。
    </div>

    <section id="partners" class="partners-section">
        <h2 id="partners-title">我们的合作伙伴</h2>
        <div class="partners-intro" id="partners-intro">
            <p>与优秀的伙伴携手，为雪友们带来更专业、更全面的滑雪体验</p>
        </div>

        <div class="partners-grid">
            <div class="partner-card">
                <img src="assets/picture/sbc_logo.png" alt="SnowboardCoach" class="partner-logo">
                <div class="partner-name" id="partner-sbc-name">SnowboardCoach</div>
                <div class="partner-description" id="partner-sbc-description">
                    英国领先的滑雪板教学机构，BASI官方合作伙伴，提供专业的滑雪板指导员培训课程
                </div>
                <a href="https://snowboardcoach.co.uk/" target="_blank" class="partner-link" id="partner-sbc-link">
                    访问官网
                </a>
            </div>

            <!-- 示例：未来可以添加更多合作伙伴 -->
            <div class="partner-card" style="opacity: 0.5; border: 2px dashed #ccc;">
                <div style="display: flex; align-items: center; justify-content: center; height: 80px; background: #f8f9fa; border-radius: 8px; margin-bottom: 1.5rem;">
                    <span style="color: #6c757d; font-size: 2rem;">+</span>
                </div>
                <div class="partner-name" style="color: #6c757d;">更多合作伙伴</div>
                <div class="partner-description" style="color: #6c757d;">
                    敬请期待更多优秀合作伙伴的加入
                </div>
                <div style="padding: 0.6rem 1.2rem; background: #e9ecef; color: #6c757d; border-radius: 6px; display: inline-block;">
                    即将推出
                </div>
            </div>
        </div>
    </section>

    <script>
        const zhTexts = {
            partnersTitle: '我们的合作伙伴',
            partnersIntro: '与优秀的伙伴携手，为雪友们带来更专业、更全面的滑雪体验',
            partnerSbcName: 'SnowboardCoach',
            partnerSbcDescription: '英国领先的滑雪板教学机构，BASI官方合作伙伴，提供专业的滑雪板指导员培训课程',
            partnerSbcLink: '访问官网'
        };

        const enTexts = {
            partnersTitle: 'Our Partners',
            partnersIntro: 'Collaborating with excellent partners to bring more professional and comprehensive snow experiences to riders',
            partnerSbcName: 'SnowboardCoach',
            partnerSbcDescription: 'UK\'s leading snowboard instruction organization, official BASI partner, providing professional snowboard instructor training courses',
            partnerSbcLink: 'Visit Website'
        };

        const nlTexts = {
            partnersTitle: 'Onze Partners',
            partnersIntro: 'Samenwerken met uitstekende partners om meer professionele en uitgebreide sneeuw ervaringen aan riders te bieden',
            partnerSbcName: 'SnowboardCoach',
            partnerSbcDescription: 'Toonaangevende snowboard instructie organisatie in het VK, officiële BASI partner, biedt professionele snowboard instructeur trainingen',
            partnerSbcLink: 'Bezoek Website'
        };

        function setLang(lang) {
            const texts = lang === 'zh' ? zhTexts : lang === 'nl' ? nlTexts : enTexts;

            document.getElementById('partners-title').textContent = texts.partnersTitle;
            document.getElementById('partners-intro').innerHTML = `<p>${texts.partnersIntro}</p>`;
            document.getElementById('partner-sbc-name').textContent = texts.partnerSbcName;
            document.getElementById('partner-sbc-description').textContent = texts.partnerSbcDescription;
            document.getElementById('partner-sbc-link').textContent = texts.partnerSbcLink;
        }

        document.getElementById('lang').addEventListener('change', function(e) {
            setLang(e.target.value);
        });

        // Initialize with Chinese
        setLang('zh');
    </script>
</body>
</html>
